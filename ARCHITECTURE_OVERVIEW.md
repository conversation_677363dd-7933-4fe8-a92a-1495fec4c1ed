# Dabot-OHLC Enhanced Architecture Overview

## Project Structure for Unified Data Aggregation

This document outlines the enhanced architecture that enables endpoints to query data from all sources through a unified aggregation system.

## Current vs Enhanced Architecture

### Current Flow
```
Endpoint → refresh_db_by_candles → [Source APIs] → store_in_db → get_from_db (single source)
```

### Enhanced Flow
```
Endpoint → UnifiedDataManager → SourceCoordinator → [All Source APIs] 
    ↓
Translators → Aggregators → store_unified_data → get_unified_data → Endpoint
```

## Directory Structure

```
app/
├── core/
│   ├── unified_data_manager.py          # Central coordinator for all data operations
│   ├── source_coordinator.py            # Manages parallel data fetching from sources
│   ├── aggregators/                     # Data aggregation components
│   │   ├── __init__.py                  # Package configuration and constants
│   │   ├── base_aggregator.py           # Abstract base class for aggregators
│   │   ├── candles_aggregator.py        # OHLC candle data aggregation
│   │   ├── range_aggregator.py          # Date range data aggregation
│   │   └── data_quality_checker.py      # Comprehensive quality assessment
│   ├── translators/                     # Source-specific data translators
│   │   ├── __init__.py                  # Unified schema definitions
│   │   ├── base_translator.py           # Abstract base translator
│   │   ├── bitstamp_translator.py       # Bitstamp data translation
│   │   ├── coindesk_translator.py       # Coindesk data translation
│   │   └── kraken_translator.py         # Kraken data translation (future)
│   └── helpers/                         # Existing helper functions
├── db/
│   ├── unified/                         # Unified database operations
│   │   ├── __init__.py                  # Package configuration
│   │   ├── store_unified_data.py        # Store aggregated data
│   │   ├── get_unified_data.py          # Retrieve unified data
│   │   ├── merge_source_data.py         # Intelligent data merging
│   │   └── unified_schema.py            # Database schema management
│   ├── data/
│   │   ├── unified/                     # Unified database storage
│   │   │   └── *.db                     # Combined data from all sources
│   │   └── sources/                     # Source-specific databases (backup)
│   │       ├── bitstamp/
│   │       ├── coindesk/
│   │       └── kraken/
│   └── sqlite/                          # Existing source-specific operations
└── config.py                           # Enhanced configuration
```

## Key Components

### 1. UnifiedDataManager (`app/core/unified_data_manager.py`)
- **Purpose**: Central coordinator for all data operations
- **Responsibilities**:
  - Handle requests from API endpoints
  - Coordinate with SourceCoordinator for data fetching
  - Manage data freshness validation
  - Orchestrate aggregation and storage
  - Provide fallback mechanisms

### 2. SourceCoordinator (`app/core/source_coordinator.py`)
- **Purpose**: Manage parallel data fetching from multiple sources
- **Responsibilities**:
  - Coordinate concurrent source requests
  - Handle source failures gracefully
  - Implement retry logic and rate limiting
  - Route data through translators

### 3. Aggregators (`app/core/aggregators/`)
- **Purpose**: Combine data from multiple sources intelligently
- **Components**:
  - `base_aggregator.py`: Common aggregation interface
  - `candles_aggregator.py`: OHLC-specific aggregation logic
  - `range_aggregator.py`: Date range optimization
  - `data_quality_checker.py`: Comprehensive quality assessment

### 4. Translators (`app/core/translators/`)
- **Purpose**: Convert source-specific formats to unified format
- **Components**:
  - `base_translator.py`: Common translation interface
  - `bitstamp_translator.py`: Bitstamp data conversion
  - `coindesk_translator.py`: Coindesk data conversion
  - `kraken_translator.py`: Future Kraken support

### 5. Unified Database (`app/db/unified/`)
- **Purpose**: Single source of truth for aggregated data
- **Components**:
  - `store_unified_data.py`: Efficient data storage
  - `get_unified_data.py`: Optimized data retrieval
  - `merge_source_data.py`: Intelligent data merging
  - `unified_schema.py`: Database schema management

## Data Flow

### 1. Endpoint Request
```python
# Endpoint calls UnifiedDataManager
data = await unified_manager.get_candles('btcusd', 'h1', 24)
```

### 2. Data Freshness Check
```python
# Check if unified data is fresh enough
if not fresh_enough:
    # Coordinate source fetching
    source_data = await source_coordinator.fetch_from_all_sources()
```

### 3. Data Translation
```python
# Each source data goes through its translator
bitstamp_data = bitstamp_translator.translate(raw_bitstamp_data)
coindesk_data = coindesk_translator.translate(raw_coindesk_data)
```

### 4. Data Aggregation
```python
# Aggregate translated data
aggregated_data = candles_aggregator.aggregate({
    'bitstamp': bitstamp_data,
    'coindesk': coindesk_data
})
```

### 5. Unified Storage
```python
# Store in unified database
store_unified_data(currency_pair, timeframe, aggregated_data, metadata)
```

### 6. Response
```python
# Return unified data to endpoint
return format_response(aggregated_data)
```

## Configuration

### Enhanced `app/config.py`
```python
# Sources configuration
SOURCES = ['bitstamp', 'coindesk']

# Aggregation settings
AGGREGATION_STRATEGY = 'quality_weighted'
DATA_QUALITY_THRESHOLD = 0.7

# Source priorities
SOURCE_PRIORITIES = {
    'bitstamp': 0.90,
    'coindesk': 0.85,
    'kraken': 0.88
}

# Quality thresholds
QUALITY_THRESHOLDS = {
    'completeness': 0.95,
    'consistency': 0.90,
    'timeliness': 300
}
```

## Benefits

### 1. Scalability
- Easy to add new sources without changing endpoints
- Parallel processing of multiple sources
- Efficient caching and storage

### 2. Reliability
- Multiple sources provide redundancy
- Graceful degradation when sources fail
- Quality-based fallback mechanisms

### 3. Data Quality
- Aggregation improves overall data quality
- Comprehensive quality assessment
- Intelligent conflict resolution

### 4. Performance
- Unified database reduces query complexity
- Optimized indexes for common queries
- Efficient caching strategies

### 5. Maintainability
- Clear separation of concerns
- Modular, extensible architecture
- Comprehensive error handling and logging

## Implementation Status

All files have been created with detailed comments explaining their intended functionality. The structure is ready for implementation with:

- ✅ Complete directory structure
- ✅ All component interfaces defined
- ✅ Comprehensive documentation
- ✅ Enhanced configuration
- ✅ Database schema design

## Next Steps

1. **Implement core components** starting with UnifiedDataManager
2. **Create unified database schema** and operations
3. **Implement translators** for existing sources
4. **Update endpoints** to use unified data
5. **Add comprehensive testing**
6. **Performance optimization**
7. **Add monitoring and alerting**

This architecture provides a solid foundation for a scalable, reliable, and maintainable multi-source data aggregation system.
