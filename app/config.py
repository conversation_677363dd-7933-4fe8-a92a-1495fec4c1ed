# Config file
EXCLUDE_CURRENT_CANDLE_BITSTAMP = True

# Logging
LOG_LEVEL = 'DEBUG'

# Sources configuration
SOURCES = ['bitstamp', 'coindesk']  # Easy to add new sources like 'kraken'

# Data aggregation settings
AGGREGATION_STRATEGY = 'quality_weighted'  # or 'volume_weighted', 'simple_average', 'best_source'
MIN_SOURCES_REQUIRED = 1  # Minimum sources needed for valid data
DATA_QUALITY_THRESHOLD = 0.7  # Minimum quality score (0-1)

# Source priorities and reliability scores (higher = better, 0-1 scale)
SOURCE_PRIORITIES = {
    'bitstamp': 0.90,   # High reliability, good API
    'coindesk': 0.85,   # Good reliability
    'kraken': 0.88      # Future: High reliability when implemented
}

# Source-specific timeouts (seconds)
SOURCE_TIMEOUTS = {
    'bitstamp': 30,
    'coindesk': 45,
    'kraken': 30
}

# Cache and freshness settings
CACHE_EXPIRY_MINUTES = {
    'm1': 2,    # 1-minute data expires after 2 minutes
    'm5': 6,    # 5-minute data expires after 6 minutes
    'm15': 16,  # 15-minute data expires after 16 minutes
    'h1': 61,   # 1-hour data expires after 61 minutes
    'h4': 241,  # 4-hour data expires after 241 minutes
    'd1': 1441  # Daily data expires after 1441 minutes (24+ hours)
}

# Quality assessment thresholds
QUALITY_THRESHOLDS = {
    'completeness': 0.95,      # 95% of expected data points
    'consistency': 0.90,       # 90% agreement between sources
    'timeliness': 300,         # Data not older than 5 minutes (seconds)
    'volume_correlation': 0.80, # 80% volume correlation between sources
    'price_reasonableness': 0.05 # 5% maximum price deviation tolerance
}

# Database settings
UNIFIED_DB_ENABLED = True  # Enable unified database functionality
UNIFIED_DB_PATH = 'app/db/data/unified/'
SOURCE_DB_PATH = 'app/db/data/'  # Keep source-specific databases for backup

# Performance settings
MAX_CONCURRENT_SOURCES = 3  # Maximum concurrent source requests
BATCH_SIZE = 1000  # Batch size for database operations
CONNECTION_POOL_SIZE = 5  # Database connection pool size

# Circuit breaker settings for source health
CIRCUIT_BREAKER = {
    'failure_threshold': 5,    # Failures before opening circuit
    'recovery_timeout': 300,   # Seconds before attempting recovery
    'success_threshold': 3     # Successes needed to close circuit
}

# Other constants
# API_KEY = 'your_api_key_here'
# DB_HOST = 'localhost'
# DB_PORT = 5432